using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Admin.Applications
{
    public class DisburseModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly AcquittalService _acquittalService;
        private readonly CompanyService _companyService;
        private readonly EmailService _emailService;

        public DisburseModel(
            ApplicationService applicationService,
            AcquittalService acquittalService,
            CompanyService companyService,
            EmailService emailService)
        {
            _applicationService = applicationService;
            _acquittalService = acquittalService;
            _companyService = companyService;
            _emailService = emailService;
        }

        public User? CurrentUser { get; set; }
        public Application? Application { get; set; }

        [BindProperty]
        public int ApplicationId { get; set; }

        [BindProperty]
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Disbursed amount must be greater than 0")]
        public double DisbursedAmount { get; set; }

        [BindProperty]
        [StringLength(500)]
        public string? DisbursementNotes { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            Application = await _applicationService.GetApplicationByIdAsync(id);

            if (Application == null)
            {
                ErrorMessage = "Application not found.";
                return RedirectToPage("/Admin/Applications");
            }

            if (Application.Status != "Approved" || Application.IsDisbursed)
            {
                ErrorMessage = "This application is not eligible for disbursement.";
                return RedirectToPage("/Admin/Applications");
            }

            ApplicationId = id;
            DisbursedAmount = Application.RequestedCash; // Default to requested amount

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (!ModelState.IsValid)
            {
                Application = await _applicationService.GetApplicationByIdAsync(ApplicationId);
                return Page();
            }

            try
            {
                Application = await _applicationService.GetApplicationByIdAsync(ApplicationId);

                if (Application == null)
                {
                    ErrorMessage = "Application not found.";
                    return RedirectToPage("/Admin/Applications");
                }

                if (Application.Status != "Approved" || Application.IsDisbursed)
                {
                    ErrorMessage = "This application is not eligible for disbursement.";
                    return RedirectToPage("/Admin/Applications");
                }

                // Update application with disbursement details
                await _applicationService.UpdateDisbursementAsync(
                    ApplicationId, 
                    DisbursedAmount, 
                    CurrentUser.Email, 
                    DisbursementNotes);

                // Create acquittal record
                await _acquittalService.CreateAcquittalAsync(ApplicationId);

                // Send notification to company
                var company = await _companyService.GetCompanyByIdAsync(Application.CompanyID);
                if (company != null)
                {
                    await _emailService.SendDisbursementNotificationEmailAsync(
                        company.Email,
                        company.Name,
                        ApplicationId,
                        DisbursedAmount,
                        Application.Description);
                }

                TempData["SuccessMessage"] = $"Funds of ${DisbursedAmount:N2} have been successfully disbursed for Application #{ApplicationId}. The company has been notified and can now submit acquittal documentation.";
                return RedirectToPage("/Admin/Applications");
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while processing the disbursement. Please try again.";
                Application = await _applicationService.GetApplicationByIdAsync(ApplicationId);
                return Page();
            }
        }
    }
}
