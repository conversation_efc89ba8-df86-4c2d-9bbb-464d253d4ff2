@page "{id:int}"
@model _Cashdisbursment_.Pages.Admin.Applications.DisburseModel
@{
    ViewData["Title"] = "Disburse Funds";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>Disburse Funds - Application #@Model.Application?.ApplicationID
                </h1>
                <a asp-page="/Admin/Applications" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Applications
                </a>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (Model.Application != null)
    {
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>Application Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Company:</label>
                                    <p class="mb-0">@Model.Application.Company?.Name</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Description:</label>
                                    <p class="mb-0">@Model.Application.Description</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Purpose:</label>
                                    <p class="mb-0">@Model.Application.Purpose</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Requested Amount:</label>
                                    <p class="mb-0 text-primary fw-bold">$@Model.Application.RequestedCash.ToString("N2")</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Date Requested:</label>
                                    <p class="mb-0">@Model.Application.DateRequested.ToString("MMM dd, yyyy")</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Status:</label>
                                    <span class="badge bg-success">@Model.Application.Status</span>
                                </div>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.Application.PDFLocation))
                        {
                            <div class="mb-3">
                                <label class="form-label fw-bold">Supporting Document:</label>
                                <div>
                                    <a href="@Model.Application.PDFLocation" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-file-pdf me-1"></i>View Document
                                    </a>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>Disbursement Form
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <input type="hidden" asp-for="ApplicationId" />
                            
                            <div class="mb-3">
                                <label asp-for="DisbursedAmount" class="form-label">Disbursed Amount *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input asp-for="DisbursedAmount" class="form-control" placeholder="0.00" />
                                </div>
                                <span asp-validation-for="DisbursedAmount" class="text-danger"></span>
                                <div class="form-text">
                                    Requested: $@Model.Application.RequestedCash.ToString("N2")
                                </div>
                            </div>

                            <div class="mb-3">
                                <label asp-for="DisbursementNotes" class="form-label">Disbursement Notes</label>
                                <textarea asp-for="DisbursementNotes" class="form-control" rows="3" 
                                         placeholder="Optional notes about the disbursement..."></textarea>
                                <span asp-validation-for="DisbursementNotes" class="text-danger"></span>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-money-bill-wave me-2"></i>Disburse Funds
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>Important Notes
                        </h6>
                        <ul class="mb-0 small">
                            <li>Once disbursed, this action cannot be undone</li>
                            <li>The company will be notified of the disbursement</li>
                            <li>An acquittal record will be automatically created</li>
                            <li>The company can then submit acquittal documentation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
